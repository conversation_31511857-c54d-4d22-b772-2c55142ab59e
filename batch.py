import json
import requests
from tqdm import tqdm
import signal
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
import random
import threading

RESULT_FILE = 'result.json'
REMAIN_FILE = 'remain.json'
FAILED_FILE = 'failed_tasks.json'
SUCCESS_LOG = 'success.log'
FAILURE_LOG = 'failures.log'
remaining_tasks = []
interrupted = False
lock = threading.Lock()

MAX_RETRIES = 3
THREADS = 10

def group_tasks(task_list):
    grouped = {}
    for url, entry in task_list:
        grouped.setdefault(url, []).append(entry)
    return grouped

def handle_sigint(signum, frame):
    global interrupted
    interrupted = True
    print("\n🛑 检测到中断信号，保存未完成任务...")
    with open(REMAIN_FILE, 'w', encoding='utf-8') as f:
        json.dump(group_tasks(remaining_tasks), f, ensure_ascii=False, indent=2)
    print(f"💾 未完成任务已保存至 {REMAIN_FILE}")
    exit(0)

signal.signal(signal.SIGINT, handle_sigint)

# ✅ 粘贴你从浏览器中复制的 Cookie 字符串
RAW_COOKIE = """
TODO
""".strip()

# ✅ 粘贴你自己的 Authorization 和 LoginUuid
AUTHORIZATION = 'TODO'
LOGIN_UUID = 'TODO'
PARENT_FILE_ID = 111111#TODO # 这里可以设置为你想要的父文件夹 ID

def parse_cookie(raw_cookie: str) -> dict:
    cookies = {}
    for part in raw_cookie.split(';'):
        if '=' in part:
            key, value = part.strip().split('=', 1)
            cookies[key] = value
    return cookies

COOKIES = parse_cookie(RAW_COOKIE)

HEADERS_TEMPLATE = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'App-Version': '3',
    'Authorization': AUTHORIZATION,
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8',
    'DNT': '1',
    'LoginUuid': LOGIN_UUID,
    'Origin': 'https://www.123pan.com',
    'Referer': '',  # 动态设置
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'headers': '[object Object]',
    'platform': 'web',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
}

def get_share_key(url):
    return url.rstrip('/').split('/')[-1]

def build_payload(file_entry, share_key):
    return {
        "file_list": [{
            "file_id": file_entry["FileId"],
            "size": file_entry.get("Size", 0),
            "etag": file_entry.get("Etag", ""),
            "type": file_entry.get("Type", 1),
            "parent_file_id": PARENT_FILE_ID,  #file_entry.get("ParentFileId", 0),
            "file_name": file_entry["FileName"],
            "drive_id": 0
        }],
        "share_key": share_key,
        "share_pwd": None,
        "current_level": 0,
        "event": "transfer",
        "fileNum": 1,
        "operatePlace": 1
    }

def process_task(task):
    url, file_entry = task
    share_key = get_share_key(url)
    headers = HEADERS_TEMPLATE.copy()
    headers['Referer'] = url
    payload = build_payload(file_entry, share_key)

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            time.sleep(random.uniform(0.3, 0.8))  # 控制速率
            response = requests.post(
                'https://www.123pan.com/b/api/file/copy/async',
                headers=headers,
                cookies=COOKIES,
                json=payload,
                timeout=10
            )
            if response.status_code == 200 and response.json().get("code") == 0:
                with lock:
                    with open(SUCCESS_LOG, 'a', encoding='utf-8') as logf:
                        logf.write(f"SUCCESS: {file_entry['FileName']}\n")
                return True
            else:
                print(f"❌ 第 {attempt} 次失败：{file_entry['FileName']} - {response.text}")
        except Exception as e:
            print(f"⚠️ 异常 第 {attempt} 次：{file_entry['FileName']} - {e}")
    with lock:
        with open(FAILURE_LOG, 'a', encoding='utf-8') as logf:
            logf.write(f"FAIL: {file_entry['FileName']}\n")
    return False

def main():
    global remaining_tasks
    global interrupted
    with open(RESULT_FILE, 'r', encoding='utf-8') as f:
        data = json.load(f)

    success = 0
    failed = 0
    failed_tasks = {}

    tasks = []
    for url, file_list in data.items():
        for file_entry in file_list:
            tasks.append((url, file_entry))

    remaining_tasks = tasks.copy()

    with ThreadPoolExecutor(max_workers=THREADS) as executor:
        futures = {executor.submit(process_task, task): task for task in tasks}
        for future in tqdm(as_completed(futures), total=len(tasks), desc="转存中", unit="项"):
            task = futures[future]
            result = future.result()
            if result:
                success += 1
            else:
                failed += 1
                failed_tasks.setdefault(task[0], []).append(task[1])

    if failed_tasks:
        with open(FAILED_FILE, 'w', encoding='utf-8') as f:
            json.dump(failed_tasks, f, ensure_ascii=False, indent=2)
        print(f"❗ 已保存失败任务至 {FAILED_FILE}")

    print(f"\n🎯 批量完成，成功：{success}，失败：{failed}")

if __name__ == '__main__':
    main()