【INFO】2024-09-28 20:38:25,246 - configcenter - 正在应用配置中心配置：{'enabled': True, 'params': '', 'GITHUB_TOKEN': None, 'API_TOKEN': 'moviepilot', 'TMDB_API_DOMAIN': 'api.themoviedb.org', 'TMDB_IMAGE_DOMAIN': 'image.tmdb.org', 'WALLPAPER': 'tmdb', 'RECOGNIZE_SOURCE': 'themoviedb', 'SCRAP_FOLLOW_TMDB': True, 'AUTO_DOWNLOAD_USER': None, 'OCR_HOST': 'https://movie-pilot.org', 'DOWNLOAD_SUBTITLE': True, 'PLUGIN_MARKET': 'https://github.com/jxxghp/MoviePilot-Plugins,https://github.com/thsrite/MoviePilot-Plugins,https://github.com/honue/MoviePilot-Plugins,https://github.com/InfinityPacer/MoviePilot-Plugins，https://github.com/jxxghp/MoviePilot-Plugins/,https://github.com/thsrite/MoviePilot-Plugins/,https://github.com/honue/MoviePilot-Plugins/,https://github.com/InfinityPacer/MoviePilot-Plugins/,https://github.com/dandkong/MoviePilot-Plugins/,https://github.com/Aqr-K/MoviePilot-Plugins/,https://github.com/AnjoyLi/MoviePilot-Plugins/,https://github.com/WithdewHua/MoviePilot-Plugins/,https://github.com/HankunYu/MoviePilot-Plugins/,https://github.com/baozaodetudou/MoviePilot-Plugins/,https://github.com/almus2zhang/MoviePilot-Plugins/,https://github.com/Pixel-LH/MoviePilot-Plugins/,https://github.com/lightolly/MoviePilot-Plugins/,https://github.com/suraxiuxiu/MoviePilot-Plugins/,https://github.com/gxterry/MoviePilot-Plugins/,https://github.com/hotlcc/MoviePilot-Plugins-Third/,https://github.com/boeto/MoviePilot-Plugins/,https://github.com/xiangt920/MoviePilot-Plugins/,https://github.com/yubanmeiqin9048/MoviePilot-Plugins/,https://github.com/loongcheung/MoviePilot-Plugins/,https://github.com/xcehnz/MoviePilot-Plugins/,https://github.com/imaliang/MoviePilot-Plugins/', 'MOVIE_RENAME_FORMAT': '{{title}}{% if year %} ({{year}}){% endif %}/{{title}}{% if year %} ({{year}}){% endif %}{% if part %}-{{part}}{% endif %}{% if videoFormat %} - {{videoFormat}}{% endif %}{% if edition %}.{{edition}}{% endif %}{% if videoCodec %}.{{videoCodec}}{% endif %}{% if audioCodec %}.{{audioCodec}}{% endif %}{% if releaseGroup %}-{{releaseGroup}}{% endif %}{{fileExt}}', 'TV_RENAME_FORMAT': "{% if season %}{% set season = '{:02d}'.format(season | int) %}{% endif %}{{title}}{% if year %} ({{year}}){% endif %}/Season {{season}}/{{title}}{% if year %} ({{year}}){% endif %}.{{season_episode}}{% if part %}-{{part}}{% endif %}{% if episode_title %}.{{episode_title}}{% endif %}{% if videoFormat %}-{{videoFormat}}{% endif %}{% if edition %}.{{edition}}-{% endif %}{% if videoCodec %}{{videoCodec}}-{% endif %}{% if audioCodec %}{{audioCodec}}-{% endif %}{% if customization %}{{customization}}@{% endif %}{{fileExt}}", 'FANART_ENABLE': True, 'DOH_ENABLE': True, 'SEARCH_MULTIPLE_NAME': False, 'META_CACHE_EXPIRE': 0, 'GITHUB_PROXY': '', 'DOH_DOMAINS': 'api.themoviedb.org,api.tmdb.org,webservice.fanart.tv,api.github.com,github.com,raw.githubusercontent.com,api.telegram.org', 'DOH_RESOLVERS': '*******,*******,*******,***************', 'undefined': True, 'writeenv': True, 'SCRAP_SOURCE': 'themoviedb'}
【INFO】2024-09-28 20:38:25,248 - module.py - 正在停止所有模块...
【INFO】2024-09-28 20:38:25,249 - module.py - Moudle Stoped：BangumiModule
【INFO】2024-09-28 20:38:25,250 - module.py - Moudle Stoped：DoubanModule
【INFO】2024-09-28 20:38:25,251 - module.py - Moudle Stoped：EmbyModule
【INFO】2024-09-28 20:38:25,253 - module.py - Moudle Stoped：FanartModule
【INFO】2024-09-28 20:38:25,254 - module.py - Moudle Stoped：FileTransferModule
【INFO】2024-09-28 20:38:25,255 - module.py - Moudle Stoped：FilterModule
【INFO】2024-09-28 20:38:25,256 - module.py - Moudle Stoped：IndexerModule
【INFO】2024-09-28 20:38:25,257 - module.py - Moudle Stoped：QbittorrentModule
【INFO】2024-09-28 20:38:25,258 - module.py - Moudle Stoped：SubtitleModule
【INFO】2024-09-28 20:38:25,259 - module.py - Moudle Stoped：TheMovieDbModule
【INFO】2024-09-28 20:38:25,260 - module.py - Moudle Stoped：TheTvDbModule
【INFO】2024-09-28 20:38:25,261 - module.py - Moudle Stoped：WebPushModule
【INFO】2024-09-28 20:38:25,262 - module.py - 模块停止完成
【INFO】2024-09-28 20:38:25,276 - module.py - Moudle Loaded：BangumiModule
【INFO】2024-09-28 20:38:25,277 - module.py - Moudle Loaded：DoubanModule
【INFO】2024-09-28 20:38:25,279 - module.py - Moudle Loaded：EmbyModule
【INFO】2024-09-28 20:38:25,280 - module.py - Moudle Loaded：FanartModule
【INFO】2024-09-28 20:38:25,281 - module.py - Moudle Loaded：FileTransferModule
【INFO】2024-09-28 20:38:25,284 - module.py - Moudle Loaded：FilterModule
【INFO】2024-09-28 20:38:25,286 - module.py - Moudle Loaded：IndexerModule
【INFO】2024-09-28 20:38:25,287 - module.py - Moudle Loaded：QbittorrentModule
【INFO】2024-09-28 20:38:25,288 - module.py - Moudle Loaded：SubtitleModule
【INFO】2024-09-28 20:38:25,290 - module.py - Moudle Loaded：TheMovieDbModule
【INFO】2024-09-28 20:38:25,291 - module.py - Moudle Loaded：TheTvDbModule
【INFO】2024-09-28 20:38:25,292 - module.py - Moudle Loaded：WebPushModule
【INFO】2024-09-28 20:38:25,315 - configcenter - app.env文件写入完成
【INFO】2024-09-28 20:38:25,316 - configcenter - 配置中心设置已写入app.env文件，插件关闭...
