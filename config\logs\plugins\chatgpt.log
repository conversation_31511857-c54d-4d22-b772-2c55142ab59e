【INFO】2024-09-29 08:35:06,453 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:35:41,500 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:43:43,922 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:43:59,538 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:50:00,448 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:51:00,064 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:51:55,469 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:52:25,213 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:53:38,522 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:53:44,593 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:53:45,478 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:53:50,733 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:57:47,283 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:57:59,352 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:58:15,620 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:58:28,854 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 08:58:53,252 - chatgpt - ChatGPT辅助识别结果：{}
【INFO】2024-09-29 09:00:04,495 - chatgpt - ChatGPT辅助识别结果：{'title': 'Go for Broke', 'version': '重生', 'part': '', 'year': '2024', 'resolution': '1080p', 'season': None, 'episode': None}
【INFO】2024-09-29 09:00:39,004 - chatgpt - ChatGPT辅助识别结果：{'title': 'Go for Broke', 'version': '2024', 'part': '', 'year': '2024', 'resolution': '1080p', 'season': None, 'episode': None}
【INFO】2024-09-29 09:04:58,620 - chatgpt - ChatGPT辅助识别结果：{'title': '卢卡 夏日友情天', 'version': '港台', 'part': 'SGNB第237部UHD原盘DIY', 'year': '2021', 'resolution': '2160p', 'season': None, 'episode': None}
【INFO】2024-09-29 09:05:16,057 - chatgpt - ChatGPT辅助识别结果：{'title': 'Indiana Jones and the Temple of Doom', 'version': 'UHD', 'part': 'SGNB 第245部', 'year': '1984', 'resolution': '2160p', 'season': None, 'episode': None}
【INFO】2024-09-29 09:46:37,758 - chatgpt - ChatGPT辅助识别结果：{'title': 'Degman', 'version': 'UHD', 'part': 'Remux', 'year': '2023', 'resolution': '2160p', 'season': None, 'episode': None}
【INFO】2024-09-29 15:55:44,793 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 6 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 15:55:57,779 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 1}
【INFO】2024-09-29 15:56:09,665 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 8}
【INFO】2024-09-29 15:56:21,947 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 3}
【INFO】2024-09-29 15:56:33,621 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': 'S01E02', 'part': '第 2 集', 'year': None, 'resolution': 'HD', 'season': 1, 'episode': 2}
【INFO】2024-09-29 15:56:44,741 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 8}
【INFO】2024-09-29 15:56:56,211 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 12 集', 'year': '', 'resolution': 'mp4', 'season': 1, 'episode': 12}
【INFO】2024-09-29 15:57:07,049 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 15:57:18,629 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 1 集', 'year': '', 'resolution': 'HD', 'season': 1, 'episode': 1}
【INFO】2024-09-29 15:57:29,304 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 15:57:40,282 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '第 11 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 11}
【INFO】2024-09-29 15:57:51,337 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 12}
【INFO】2024-09-29 15:58:01,912 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 15:58:12,773 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 11}
【INFO】2024-09-29 15:58:24,316 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 4 集', 'year': '', 'resolution': 'unknown', 'season': 1, 'episode': 4}
【INFO】2024-09-29 15:58:36,044 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 4}
【INFO】2024-09-29 15:58:47,103 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 2}
【INFO】2024-09-29 15:58:59,457 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 7}
【INFO】2024-09-29 15:59:10,729 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 3 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 3}
【INFO】2024-09-29 15:59:21,411 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': 'S01E12', 'part': '第 12 集', 'year': None, 'resolution': '1080p', 'season': 1, 'episode': 12}
【INFO】2024-09-29 15:59:33,502 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 7}
【INFO】2024-09-29 15:59:44,897 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01E05', 'part': '第 5 集', 'year': None, 'resolution': '720p', 'season': 1, 'episode': 5}
【INFO】2024-09-29 15:59:56,269 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': None, 'part': '第 1 集', 'year': None, 'resolution': 'unknown', 'season': 1, 'episode': 1}
【INFO】2024-09-29 16:00:09,397 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:00:21,193 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 5}
【INFO】2024-09-29 16:00:32,201 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:00:43,422 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 5}
【INFO】2024-09-29 16:00:54,625 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 2 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 2}
【INFO】2024-09-29 16:01:05,813 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 3}
【INFO】2024-09-29 16:01:17,013 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:01:28,112 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': 'S01E04', 'part': '第 4 集', 'year': None, 'resolution': '1080p', 'season': 1, 'episode': 4}
【INFO】2024-09-29 16:01:39,416 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': None, 'part': '第 11 集', 'year': None, 'resolution': 'mkv', 'season': 1, 'episode': 11}
【INFO】2024-09-29 16:01:50,624 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '第 9 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 16:02:01,552 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 16:02:12,929 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '第 7 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 7}
【INFO】2024-09-29 16:02:24,806 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 8}
【INFO】2024-09-29 16:24:59,036 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01E01', 'part': '第 1 集', 'year': None, 'resolution': '1080p', 'season': 1, 'episode': 1}
【INFO】2024-09-29 16:25:09,811 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 2 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 2}
【INFO】2024-09-29 16:25:20,874 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': None, 'part': '第 3 集', 'year': None, 'resolution': 'null', 'season': 1, 'episode': 3}
【INFO】2024-09-29 16:25:33,259 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 4 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 4}
【INFO】2024-09-29 16:25:44,717 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 5 集', 'year': '', 'resolution': '1080p', 'season': 1, 'episode': 5}
【INFO】2024-09-29 16:25:56,288 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 16:26:07,780 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': 'SD', 'season': 1, 'episode': 7}
【INFO】2024-09-29 16:26:18,672 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 8 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 8}
【INFO】2024-09-29 16:26:30,973 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 16:26:43,324 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01E10', 'part': '第 10 集', 'year': None, 'resolution': 'mp4', 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:26:54,560 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 11}
【INFO】2024-09-29 16:27:07,196 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 12}
【INFO】2024-09-29 16:27:39,205 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:27:50,585 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 1 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 1}
【INFO】2024-09-29 16:28:01,833 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': None, 'part': None, 'year': None, 'resolution': None, 'season': 1, 'episode': 10}
【INFO】2024-09-29 16:28:15,001 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '第 11 集', 'year': '', 'resolution': 'HD', 'season': 1, 'episode': 11}
【INFO】2024-09-29 16:28:49,967 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 9 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 16:29:01,621 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '第 11 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 11}
【INFO】2024-09-29 16:29:12,940 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 6 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 16:29:24,384 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '第 8 集', 'year': '', 'resolution': '', 'season': 1, 'episode': 8}
【INFO】2024-09-29 16:29:35,721 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01', 'part': '12', 'year': None, 'resolution': 'unknown', 'season': 1, 'episode': 12}
【INFO】2024-09-29 16:29:46,786 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': 'S01E08', 'part': '第 8 集', 'year': None, 'resolution': 'HD', 'season': 1, 'episode': 8}
【INFO】2024-09-29 16:29:58,345 - chatgpt - ChatGPT辅助识别结果：{'title': '靠废柴技能【状态异常】成为最强的我将蹂躏一切', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 12}
【INFO】2024-09-29 16:30:09,627 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01', 'part': '第 3 集', 'year': None, 'resolution': 'unknown', 'season': 1, 'episode': 3}
【INFO】2024-09-29 16:30:20,834 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 6}
【INFO】2024-09-29 16:30:32,164 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 5}
【INFO】2024-09-29 16:30:44,819 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 5}
【INFO】2024-09-29 16:30:55,732 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '', 'resolution': '', 'season': 1, 'episode': 9}
【INFO】2024-09-29 16:31:06,455 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '2024', 'resolution': ' ', 'season': 1, 'episode': 7}
【INFO】2024-09-29 16:31:18,405 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': None, 'part': None, 'year': '2024', 'resolution': 'Unknown', 'season': 1, 'episode': 1}
【INFO】2024-09-29 16:31:29,543 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '2024', 'resolution': 'MP4', 'season': 1, 'episode': 6}
【INFO】2024-09-29 16:31:40,803 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': 'S01E01', 'part': '前辈是男孩子', 'year': '2024', 'resolution': 'unknown', 'season': 1, 'episode': 1}
【INFO】2024-09-29 16:31:51,846 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '2024', 'resolution': 'unknown', 'season': 1, 'episode': 11}
【INFO】2024-09-29 16:32:03,021 - chatgpt - ChatGPT辅助识别结果：{'title': '前辈是男孩子', 'version': '', 'part': '可爱之物巡礼', 'year': '2024', 'resolution': '1080p', 'season': 1, 'episode': 2}
【INFO】2024-09-29 16:32:13,907 - chatgpt - ChatGPT辅助识别结果：{'title': '现代误译', 'version': '', 'part': '', 'year': '2024', 'resolution': 'HD', 'season': 1, 'episode': 12}
