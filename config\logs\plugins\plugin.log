【INFO】2024-09-29 09:46:37,818 - chain - 发送消息：channel=None，title=Degman.2023.2160p.UHD.Bluray.Remux.DV.HDR.HEVC.TrueHD.Atmos.7.1-老K.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 102 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【INFO】2024-09-29 10:23:39,741 - chain - 发送消息：channel=MessageChannel.Telegram，title=根据你提供的信息，UID代表用户ID，CID代表客户端ID，SEID代表会话ID。这些信息通常用于识别用户、设备或会话。在网络应用程序中，这些信息可以用于跟踪用户的活动或实现个性化的功能。比如，UID可以用来查看特定用户的信息和偏好，CID可以用来标识特定设备或客户端，SEID可以用来跟踪用户在网站上的活动。这些信息有助于提高用户体验和安全性。, text=None，userid=1280554894
【INFO】2024-09-29 14:05:58,862 - chain - 发送消息：channel=None，title=【站点自动签到】, text=全部签到数量: 4 
本次签到数量: 4 
下次签到数量: 0 
【春天】签到成功
【天空】签到成功
【我堡】签到成功
【观众】签到成功，userid=None
【INFO】2024-09-29 14:06:03,778 - chain - 发送消息：channel=None，title=【站点自动登录】, text=全部登录数量: 4 
本次登录数量: 4 
下次登录数量: 0 
【春天】模拟登录成功
【天空】模拟登录成功
【我堡】模拟登录成功
【观众】模拟登录成功，userid=None
【INFO】2024-09-29 15:55:45,235 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E06 - 第 6 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 144 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:55:45,251 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+144+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1844be50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:55:48,256 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+144+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfd96dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:55:54,261 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+144+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18627750>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:55:58,120 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E01 - 第 1 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 145 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:55:58,133 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+145+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c005c3e10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:01,138 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+145+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18468210>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:56:07,144 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+145+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18469710>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:56:09,951 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E08 - 第 8 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 146 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:56:09,971 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+146+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3c105fd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:12,976 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+146+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c183fd0d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:56:18,982 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+146+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c2ed950>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:56:22,332 - chain - 发送消息：channel=None，title=现代误译 - S01E03 - 第 3 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 147 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:56:22,345 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+147+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c185bee50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:25,350 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+147+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c185fd110>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:56:31,356 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+147+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1843ed90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:56:33,717 - chain - 发送消息：channel=None，title=现代误译 - S01E02 - 第 2 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 148 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:56:33,735 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+148+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c231f10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:36,739 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+148+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1842e7d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:56:42,744 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+148+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdf0d9e90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:56:45,058 - chain - 发送消息：channel=None，title=现代误译 - S01E08 - 第 8 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 149 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:56:45,071 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+149+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18626c90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:48,078 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+149+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfaa4c90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:56:54,084 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+149+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdaf1f2d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:56:56,446 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E12 - 第 12 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 150 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:56:56,467 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+150+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc7828d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:56:59,472 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+150+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c0065f010>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:57:05,477 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+150+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18450e10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:57:07,287 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E09 - 第 9 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 151 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:57:07,305 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+151+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1843ed10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:57:10,310 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+151+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18434990>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:57:16,314 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+151+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e968a50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:57:18,737 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E01 - 第 1 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 152 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:57:18,750 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+152+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3f6e2c10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:57:21,758 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+152+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18689510>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:57:27,764 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+152+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c233b50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:57:29,589 - chain - 发送消息：channel=None，title=现代误译 - S01E06 - 第 6 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 153 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:57:29,606 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+153+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e881590>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:57:32,611 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+153+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfe9d7d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:57:38,617 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+153+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00631250>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:57:40,425 - chain - 发送消息：channel=None，title=现代误译 - S01E11 - 第 11 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 154 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:57:40,441 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+154+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c51a34d10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:57:43,446 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+154+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c2ed190>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:57:49,453 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+154+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e394510>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:57:51,410 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E12 - 第 12 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 155 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:57:51,424 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+155+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf2d9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:57:54,429 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+155+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c005906d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:00,434 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+155+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4fe170d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:02,282 - chain - 发送消息：channel=None，title=现代误译 - S01E09 - 第 9 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 156 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:58:02,299 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+156+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00686e10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:58:05,305 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+156+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1854f090>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:11,309 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+156+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde7e8a90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:13,156 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E11 - 第 11 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 157 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:58:13,174 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+157+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18462a50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:58:16,180 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+157+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde64eb50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:22,185 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+157+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcd6ab10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:24,761 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E04 - 第 4 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 158 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:58:24,774 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+158+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdedad910>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:58:27,779 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+158+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c518a7550>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:33,785 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+158+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c23abd0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:36,243 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E04 - 第 4 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 159 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:58:36,269 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+159+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c004972d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:58:39,276 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+159+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb07ed10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:45,282 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+159+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfaa7a10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:47,613 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E02 - 第 2 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 160 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:58:47,627 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+160+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00595c50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:58:50,632 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+160+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb0605d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:58:56,637 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+160+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18688950>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:58:59,985 - chain - 发送消息：channel=None，title=现代误译 - S01E07 - 第 7 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 161 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:00,003 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+161+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc308ed0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:03,011 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+161+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc754dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:59:09,016 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+161+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c183ff9d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:59:10,812 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E03 - 第 3 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 162 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:10,826 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+162+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf2ef50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:13,831 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+162+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18419a50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:59:19,836 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+162+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f846d90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:59:21,664 - chain - 发送消息：channel=None，title=现代误译 - S01E12 - 第 12 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 163 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:21,677 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+163+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e3fb090>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:24,683 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+163+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1840cd50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:59:30,688 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+163+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde7644d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:59:33,857 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E07 - 第 7 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 164 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:33,871 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+164+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1841b510>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:36,877 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+164+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc756750>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:59:42,882 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+164+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c184c4d50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:59:45,215 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E05 - 第 5 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 165 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:45,229 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+165+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00673a50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:48,234 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+165+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc7809d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 15:59:54,240 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+165+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdf0dba10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 15:59:56,553 - chain - 发送消息：channel=None，title=现代误译 - S01E01 - 第 1 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 166 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 15:59:56,566 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+166+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb0a7790>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 15:59:59,570 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+166+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c184c64d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:00:05,574 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+166+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde9fdb90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:00:09,840 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E10 - 第 10 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 167 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:00:09,855 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+167+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1854f090>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:00:12,860 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+167+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb060890>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:00:18,865 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+167+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c184c50d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:00:21,243 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E05 - 第 5 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 168 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:00:21,256 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+168+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18419410>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:00:24,261 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+168+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdd0ea250>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:00:30,266 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+168+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf2eed0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:00:32,688 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E10 - 第 10 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 169 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:00:32,701 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+169+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e784610>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:00:35,707 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+169+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00662c50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:00:41,714 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+169+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde4d1910>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:00:43,574 - chain - 发送消息：channel=None，title=现代误译 - S01E05 - 第 5 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 170 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:00:43,603 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+170+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c0063f710>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:00:46,612 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+170+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00667250>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:00:52,617 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+170+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00653ad0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:00:55,011 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E02 - 第 2 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 171 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:00:55,024 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+171+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f3ed150>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:00:58,029 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+171+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18551310>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:01:04,034 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E02+-+%E7%AC%AC+2+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+171+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f0f2b50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:01:06,339 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E03 - 第 3 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 172 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:01:06,354 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+172+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdee03390>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:01:09,359 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+172+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc30bf10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:01:15,370 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+172+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c0065e950>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:01:17,335 - chain - 发送消息：channel=None，title=现代误译 - S01E10 - 第 10 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 173 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:01:17,354 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+173+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb062ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:01:20,359 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+173+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c2ed150>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:01:26,363 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+173+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc752a50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:01:28,201 - chain - 发送消息：channel=None，title=现代误译 - S01E04 - 第 4 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 174 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:01:28,232 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+174+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c518979d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:01:31,244 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+174+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcd4e210>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:01:37,250 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E04+-+%E7%AC%AC+4+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+174+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e3ac290>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:01:39,604 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E11 - 第 11 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 175 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:01:39,623 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+175+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18627750>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:01:42,628 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+175+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00633390>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:01:48,632 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+175+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1851bd90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:01:51,029 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E09 - 第 9 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 176 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:01:51,043 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+176+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c003ded10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:01:54,049 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+176+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bded5d8d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:02:00,055 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+176+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf2f3d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:02:01,934 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E06 - 第 6 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 177 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:02:01,962 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+177+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00667dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:02:04,967 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+177+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f6a91d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:02:10,971 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+177+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc782c50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:02:13,278 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E07 - 第 7 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 178 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:02:13,296 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+178+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18662790>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:02:16,302 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+178+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf40690>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:02:22,306 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E07+-+%E7%AC%AC+7+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+178+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c518a5b90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:02:25,235 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E08 - 第 8 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 179 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:02:25,257 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+179+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3c17b7d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:02:28,269 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+179+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf41590>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:02:34,274 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+179+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c231f10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:27:39,587 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E10 - 第 10 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 192 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:27:39,600 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+192+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c184c6cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:27:42,609 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+192+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c518c0110>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:27:48,614 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+192+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdd0ea790>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:27:50,908 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E01 - 第 1 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 193 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:27:50,924 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+193+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18622410>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:27:53,929 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+193+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde7ea510>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:27:59,937 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E01+-+%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+193+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18422bd0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:28:02,288 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E10 - 第 10 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 194 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:28:02,304 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+194+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e784e90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:28:05,309 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+194+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c233dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:28:11,314 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E10+-+%E7%AC%AC+10+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+194+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc7d49d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:28:15,054 - chain - 发送消息：channel=None，title=现代误译 - S01E11 - 第 11 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 195 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:28:15,067 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+195+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e3152d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:28:18,072 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+195+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3f462190>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:28:24,080 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+195+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18534490>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:28:50,381 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E09 - 第 9 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 196 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:28:50,401 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+196+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f1f90d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:28:53,406 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+196+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e3aee50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:28:59,412 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+196+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc7511d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:01,765 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E11 - 第 11 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 197 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:01,781 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+197+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4fb40450>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:29:04,787 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+197+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00686f90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:29:10,791 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E11+-+%E7%AC%AC+11+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+197+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdf0dbb10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:13,119 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E06 - 第 6 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 198 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:13,145 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+198+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1841b590>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:29:16,150 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+198+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde764e90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:29:22,158 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+198+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18689210>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:24,739 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E08 - 第 8 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 199 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:24,755 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+199+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdf359310>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:29:27,760 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+199+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc753ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:29:33,766 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+199+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18509bd0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:36,173 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E12 - 第 12 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 200 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:36,189 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+200+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e34da90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:29:39,194 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+200+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e3bd910>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:29:45,199 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+200+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1846ad10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:47,073 - chain - 发送消息：channel=None，title=现代误译 - S01E08 - 第 8 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 201 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:47,088 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+201+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdd0e8d90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:29:50,093 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+201+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc725510>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:29:56,098 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E08+-+%E7%AC%AC+8+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+201+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7be42ced50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:29:58,548 - chain - 发送消息：channel=None，title=靠废柴技能【状态异常】成为最强的我将蹂躏一切 - S01E12 - 第 12 集.mkv 未识别到媒体信息，无法入库！
回复：```
/redo 202 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:29:58,561 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+202+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1854cc50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:01,566 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+202+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18627990>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:30:07,570 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E9%9D%A0%E5%BA%9F%E6%9F%B4%E6%8A%80%E8%83%BD%E3%80%90%E7%8A%B6%E6%80%81%E5%BC%82%E5%B8%B8%E3%80%91%E6%88%90%E4%B8%BA%E6%9C%80%E5%BC%BA%E7%9A%84%E6%88%91%E5%B0%86%E8%B9%82%E8%BA%8F%E4%B8%80%E5%88%87+-+S01E12+-+%E7%AC%AC+12+%E9%9B%86.mkv+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+202+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3c179ed0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:30:09,865 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E03 - 第 3 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 203 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:30:09,879 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+203+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde64d550>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:12,886 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+203+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c1868a190>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:30:18,897 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E03+-+%E7%AC%AC+3+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+203+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfd97fd0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:30:21,230 - chain - 发送消息：channel=None，title=现代误译 - S01E06 - 第 6 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 204 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:30:21,244 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+204+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c3f130e50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:24,248 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+204+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc781b90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:30:30,253 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E06+-+%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+204+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfe9e090>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:30:32,469 - chain - 发送消息：channel=None，title=前辈是男孩子 - S01E05 - 第 5 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 205 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:30:32,485 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+205+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00687f90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:35,491 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+205+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde4d29d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:30:41,497 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+205+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc726e50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:30:45,039 - chain - 发送消息：channel=None，title=现代误译 - S01E05 - 第 5 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 206 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:30:45,059 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+206+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e39f110>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:48,064 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+206+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18454e90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:30:54,073 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E05+-+%E7%AC%AC+5+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+206+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdcf2f5d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:30:55,994 - chain - 发送消息：channel=None，title=现代误译 - S01E09 - 第 9 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 207 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:30:56,008 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+207+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde64ce90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:30:59,014 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+207+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4c2323d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:31:05,019 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+-+S01E09+-+%E7%AC%AC+9+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+207+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bded5f510>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:31:06,972 - chain - 发送消息：channel=None，title=现代误译 (2024) S01E07-第 7 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 208 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:31:06,996 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E07-%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+208+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdf0d89d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:31:10,001 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E07-%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+208+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb07fe50>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:31:16,006 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E07-%E7%AC%AC+7+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+208+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc780c10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:31:18,908 - chain - 发送消息：channel=None，title=现代误译 (2024) S01E01-第 1 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 209 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:31:18,921 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E01-%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+209+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e395310>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:31:21,925 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E01-%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+209+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb07e050>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:31:27,930 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E01-%E7%AC%AC+1+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+209+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb07f610>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:31:29,728 - chain - 发送消息：channel=None，title=现代误译 (2024) S01E06-第 6 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 210 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:31:29,748 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E06-%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+210+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c006548d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:31:32,764 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E06-%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+210+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c00652490>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:31:38,769 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E06-%E7%AC%AC+6+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+210+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde4d3890>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:31:40,921 - chain - 发送消息：channel=None，title=前辈是男孩子 (2024) S01E01-前辈是男孩子.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 211 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:31:40,945 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E01-%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+211+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e8815d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:31:43,951 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E01-%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+211+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdc750cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:31:49,959 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E01-%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+211+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c18406c90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:31:52,353 - chain - 发送消息：channel=None，title=现代误译 (2024) S01E11-第 11 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 212 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:31:52,368 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E11-%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+212+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4e394d10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:31:55,373 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E11-%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+212+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb0a5910>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:32:01,377 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E11-%E7%AC%AC+11+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+212+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdb0a6490>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:32:03,293 - chain - 发送消息：channel=None，title=前辈是男孩子 (2024) S01E02-可爱之物巡礼.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 213 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:32:03,317 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E02-%E5%8F%AF%E7%88%B1%E4%B9%8B%E7%89%A9%E5%B7%A1%E7%A4%BC.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+213+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bdfb255d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:32:06,322 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E02-%E5%8F%AF%E7%88%B1%E4%B9%8B%E7%89%A9%E5%B7%A1%E7%A4%BC.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+213+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c4f88c650>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:32:12,327 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E5%89%8D%E8%BE%88%E6%98%AF%E7%94%B7%E5%AD%A9%E5%AD%90+%282024%29+S01E02-%E5%8F%AF%E7%88%B1%E4%B9%8B%E7%89%A9%E5%B7%A1%E7%A4%BC.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+213+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c507c0990>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:32:14,121 - chain - 发送消息：channel=None，title=现代误译 (2024) S01E12-第 12 集.mp4 未识别到媒体信息，无法入库！
回复：```
/redo 214 [tmdbid]|[类型]
``` 手动识别转移。, text=None，userid=None
【WARNING】2024-09-29 16:32:14,134 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E12-%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+214+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7c0065d7d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:32:17,140 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E7%8E%B0%E4%BB%A3%E8%AF%AF%E8%AF%91+%282024%29+S01E12-%E7%AC%AC+12+%E9%9B%86.mp4+%E6%9C%AA%E8%AF%86%E5%88%AB%E5%88%B0%E5%AA%92%E4%BD%93%E4%BF%A1%E6%81%AF%EF%BC%8C%E6%97%A0%E6%B3%95%E5%85%A5%E5%BA%93%EF%BC%81%0A%E5%9B%9E%E5%A4%8D%EF%BC%9A%60%60%60%0A%2Fredo+214+%5Btmdbid%5D%7C%5B%E7%B1%BB%E5%9E%8B%5D%0A%60%60%60+%E6%89%8B%E5%8A%A8%E8%AF%86%E5%88%AB%E8%BD%AC%E7%A7%BB%E3%80%82%2A&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7bde64d0d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【INFO】2024-09-29 16:32:54,333 - chain - 发送消息：channel=None，title=【Telegram发生了错误】, text=HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/deleteMyCommands (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f42a8000150>: Failed to establish a new connection: [Errno 111] Connection refused')))，userid=None
【WARNING】2024-09-29 16:32:54,473 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f42a8000150%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f42ac68dbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【INFO】2024-09-29 16:32:57,203 - chain - 发送消息：channel=None，title=【TheMovieDb发生了错误】, text=无法连接TheMovieDb，请检查网络连接！，userid=None
【WARNING】2024-09-29 16:32:57,217 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f4296854390>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:32:57,478 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f42a8000150%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f42947a6c10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【WARNING】2024-09-29 16:33:00,224 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f42947ad510>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:33:03,482 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f42a8000150%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f4294786650>: Failed to establish a new connection: [Errno 111] Connection refused')))
【ERROR】2024-09-29 16:33:06,228 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f42947ac3d0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:37:47,167 - chain - 发送消息：channel=None，title=【Telegram发生了错误】, text=HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/deleteMyCommands (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5ec560e10>: Failed to establish a new connection: [Errno 111] Connection refused')))，userid=None
【WARNING】2024-09-29 16:37:47,446 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fa5ec560e10%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5f467bfd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【INFO】2024-09-29 16:37:50,088 - chain - 发送消息：channel=None，title=【TheMovieDb发生了错误】, text=无法连接TheMovieDb，请检查网络连接！，userid=None
【WARNING】2024-09-29 16:37:50,118 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5df4c0310>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:37:50,558 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fa5ec560e10%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5dc7b2290>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【WARNING】2024-09-29 16:37:53,123 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5df4d86d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:37:56,563 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fa5ec560e10%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5dc7b4a50>: Failed to establish a new connection: [Errno 111] Connection refused')))
【ERROR】2024-09-29 16:37:59,128 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fa5dc7b7050>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:56:51,054 - chain - 发送消息：channel=None，title=【Telegram发生了错误】, text=HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/deleteMyCommands (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f6693103f50>: Failed to establish a new connection: [Errno 111] Connection refused')))，userid=None
【WARNING】2024-09-29 16:56:51,181 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f6693103f50%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f66907ca990>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【INFO】2024-09-29 16:56:53,981 - chain - 发送消息：channel=None，title=【TheMovieDb发生了错误】, text=无法连接TheMovieDb，请检查网络连接！，userid=None
【WARNING】2024-09-29 16:56:53,993 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f66ac095e90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:56:54,185 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f6693103f50%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f667bf2fd90>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【WARNING】2024-09-29 16:56:56,996 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f667bf4cb10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:57:00,188 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f6693103f50%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f667bf4ed90>: Failed to establish a new connection: [Errno 111] Connection refused')))
【ERROR】2024-09-29 16:57:02,999 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f667bf4f990>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 16:57:55,232 - chain - 发送消息：channel=None，title=【Telegram发生了错误】, text=HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/deleteMyCommands (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b73293890>: Failed to establish a new connection: [Errno 111] Connection refused')))，userid=None
【WARNING】2024-09-29 16:57:55,405 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f7b73293890%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b73053cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【INFO】2024-09-29 16:57:58,117 - chain - 发送消息：channel=None，title=【TheMovieDb发生了错误】, text=无法连接TheMovieDb，请检查网络连接！，userid=None
【WARNING】2024-09-29 16:57:58,132 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b81b0aed0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 16:57:58,503 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f7b73293890%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b701c8d10>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【WARNING】2024-09-29 16:58:01,135 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b701ca390>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 16:58:04,506 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7f7b73293890%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b73028890>: Failed to establish a new connection: [Errno 111] Connection refused')))
【ERROR】2024-09-29 16:58:07,142 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f7b51f0cc10>: Failed to establish a new connection: [Errno 111] Connection refused')))
【INFO】2024-09-29 17:04:33,193 - chain - 发送消息：channel=None，title=【Telegram发生了错误】, text=HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/deleteMyCommands (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc54d5145d0>: Failed to establish a new connection: [Errno 111] Connection refused')))，userid=None
【WARNING】2024-09-29 17:04:33,344 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fc54d5145d0%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc548ee0290>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【INFO】2024-09-29 17:04:36,124 - chain - 发送消息：channel=None，title=【TheMovieDb发生了错误】, text=无法连接TheMovieDb，请检查网络连接！，userid=None
【WARNING】2024-09-29 17:04:36,136 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc54d3751d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 3 秒后重试 ...
【WARNING】2024-09-29 17:04:36,348 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fc54d5145d0%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc5295179d0>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【WARNING】2024-09-29 17:04:39,140 - common.py - HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc529535450>: Failed to establish a new connection: [Errno 111] Connection refused'))), 6 秒后重试 ...
【ERROR】2024-09-29 17:04:42,351 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90Telegram%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0AHTTPSConnectionPool%28host%3D%27api.telegram.org%27%2C+port%3D443%29%3A+Max+retries+exceeded+with+url%3A+%2Fbot6532288941%3AAAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU%2FdeleteMyCommands+%28Caused+by+ProxyError%28%27Unable+to+connect+to+proxy%27%2C+NewConnectionError%28%27%3Curllib3.connection.HTTPSConnection+object+at+0x7fc54d5145d0%3E%3A+Failed+to+establish+a+new+connection%3A+%5BErrno+111%5D+Connection+refused%27%29%29%29&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc529536cd0>: Failed to establish a new connection: [Errno 111] Connection refused')))
【ERROR】2024-09-29 17:04:45,143 - telegram.py - 发送消息失败：HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot6532288941:AAHfCfBUHV98FiZoYTZ6uIaC39GKGD3tgzU/sendMessage?chat_id=1280554894&text=%2A%E3%80%90TheMovieDb%E5%8F%91%E7%94%9F%E4%BA%86%E9%94%99%E8%AF%AF%E3%80%91%2A%0A%E6%97%A0%E6%B3%95%E8%BF%9E%E6%8E%A5TheMovieDb%EF%BC%8C%E8%AF%B7%E6%A3%80%E6%9F%A5%E7%BD%91%E7%BB%9C%E8%BF%9E%E6%8E%A5%EF%BC%81&parse_mode=Markdown (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc529535850>: Failed to establish a new connection: [Errno 111] Connection refused')))
