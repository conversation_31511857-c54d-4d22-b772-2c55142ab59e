import os
import random
import subprocess
import re
import sys # 用于获取脚本路径

# --- 配置常量 ---
# ROOT_DIR 会在 main 函数中定义和获取绝对路径
FFMPEG_IMAGE = "jrottenberg/ffmpeg"
VIDEO_EXTENSIONS = (".mp4",)

# 获取脚本所在的目录，用于存放日志文件
try:
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
except NameError: # 如果在交互式环境等 __file__ 未定义的情况
    SCRIPT_DIR = os.path.dirname(os.path.abspath(sys.argv[0]))

PROCESSED_DIRS_LOG_FILE = os.path.join(SCRIPT_DIR, "processed_poster_dirs.log")

# --- 日志处理函数 ---
def load_processed_dirs(log_file):
    """从日志文件加载已成功处理的目录路径集合"""
    processed = set()
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    processed.add(line.strip())
        except IOError as e:
            print(f"⚠️ 无法读取已处理目录日志 '{log_file}': {e}")
    return processed

def mark_dir_as_processed(dir_path, processed_set, log_file):
    """将目录标记为已处理，并更新日志文件和当前集合"""
    abs_dir_path = os.path.abspath(dir_path) # 确保使用绝对路径
    if abs_dir_path not in processed_set:
        processed_set.add(abs_dir_path)
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(abs_dir_path + "\n")
            print(f"🖊️ 已记录目录为成功处理: {abs_dir_path}")
        except IOError as e:
            print(f"⚠️ 无法写入已处理目录日志 '{log_file}': {e}")

# --- FFmpeg 相关函数 (与之前版本类似，但 generate_poster 返回布尔值) ---
def detect_black_frames(processing_dirpath, video_filename):
    host_video_symlink_path = os.path.join(processing_dirpath, video_filename)
    host_video_real_path = os.path.realpath(host_video_symlink_path)
    host_video_real_dir = os.path.dirname(host_video_real_path)
    container_video_filename = os.path.basename(host_video_real_path)
    container_video_input_path = f"/input_video_data/{container_video_filename}"
    cmd = [
        "docker", "run", "--rm",
        "-v", f"{host_video_real_dir}:/input_video_data:ro",
        FFMPEG_IMAGE,
        "-i", container_video_input_path,
        "-vf", "blackdetect=d=0.5:pic_th=0.98",
        "-an", "-t", "60",
        "-f", "null", "-"
    ]
    try:
        # print(f"🔧 [黑场检测] 执行 Docker 命令: {' '.join(cmd)}") # 可以取消注释以进行调试
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='replace')
        output = result.stderr
        black_times = []
        for match in re.finditer(r"black_start:(\d+\.?\d*) black_end:(\d+\.?\d*)", output):
            start, end = float(match.group(1)), float(match.group(2))
            black_times.append((start, end))
        return black_times
    except subprocess.CalledProcessError as e:
        print(f"❌ [黑场检测] ffmpeg 执行失败: {video_filename}。错误: {e.stderr}")
        return []
    except Exception as e:
        print(f"❌ [黑场检测] 发生未知错误: {video_filename}。错误: {e}")
        return []

def get_non_black_timestamp(black_ranges, total_duration=60):
    safe_ranges = []
    current_time = 0.0
    min_safe_duration = 2.0
    for start, end in sorted(black_ranges):
        if start - current_time >= min_safe_duration:
            safe_ranges.append((current_time, start))
        current_time = max(current_time, end)
    if total_duration - current_time >= min_safe_duration:
        safe_ranges.append((current_time, total_duration))
    if not safe_ranges:
        safe_start = 1
        safe_end = max(1, int(total_duration) - 1)
        if safe_start > safe_end:
            return safe_start if total_duration >= 1 else 0
        return random.randint(safe_start, safe_end)
    selected_range = random.choice(safe_ranges)
    rand_start = int(selected_range[0]) + 1
    rand_end = int(selected_range[1]) - 1
    if rand_start > rand_end:
        return int((selected_range[0] + selected_range[1]) / 2)
    return random.randint(rand_start, rand_end)

def generate_poster(output_dirpath, video_filename_in_dir):
    """生成 poster.jpg，成功返回 True，失败返回 False"""
    host_video_symlink_path = os.path.join(output_dirpath, video_filename_in_dir)
    host_video_real_path = os.path.realpath(host_video_symlink_path)
    host_video_real_dir = os.path.dirname(host_video_real_path)
    container_video_filename = os.path.basename(host_video_real_path)
    container_input_video_path = f"/input_video_data/{container_video_filename}"
    container_output_poster_path = "/output_data/poster.jpg"

    print(f"🔍 正在为 '{video_filename_in_dir}' 检测黑场 (真实路径: {host_video_real_path})")
    black_ranges = detect_black_frames(output_dirpath, video_filename_in_dir)
    timestamp = get_non_black_timestamp(black_ranges)

    cmd = [
        "docker", "run", "--rm",
        "-v", f"{host_video_real_dir}:/input_video_data:ro",
        "-v", f"{output_dirpath}:/output_data",
        FFMPEG_IMAGE,
        "-y",
        "-ss", str(timestamp),
        "-i", container_input_video_path,
        "-frames:v", "1",
        "-q:v", "2",
        container_output_poster_path
    ]
    print(f"🎬 正在为 '{video_filename_in_dir}' 抽取 poster.jpg 于 '{output_dirpath}' (时间点: {timestamp}s)")
    # print(f"🔧 [生成海报] 执行 Docker 命令: {' '.join(cmd)}") # 可以取消注释以进行调试
    try:
        process_result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.PIPE, check=True, text=True, encoding='utf-8', errors='replace')
        final_poster_path = os.path.join(output_dirpath, 'poster.jpg')
        if os.path.exists(final_poster_path) and os.path.getsize(final_poster_path) > 0:
            print(f"✅ 成功生成 poster.jpg: {final_poster_path}")
            return True
        else:
            print(f"⚠️ 生成 poster.jpg 后文件不存在或为空: {final_poster_path}。FFmpeg stderr: {process_result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ [生成海报] ffmpeg 执行失败: {video_filename_in_dir}。错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ [生成海报] 生成 poster.jpg 时发生未知错误: {video_filename_in_dir}。错误: {e}")
        return False

# --- 主逻辑 ---
def main():
    ROOT_DIR = "/mnt/nvme/softlink/整理影视/TV Drama/短剧/2025"  # <-- 修改为你的顶层软链接目录
    abs_root_dir = os.path.abspath(ROOT_DIR)

    if not os.path.isdir(abs_root_dir):
        print(f"❌ 根目录 '{abs_root_dir}' 不存在或不是一个目录。请检查 ROOT_DIR 设置。")
        return

    processed_dirs_set = load_processed_dirs(PROCESSED_DIRS_LOG_FILE)
    print(f"ℹ️ 已从日志加载 {len(processed_dirs_set)} 个已处理目录。")

    for dirpath, dirnames, filenames in os.walk(abs_root_dir, followlinks=False):
        current_abs_dirpath = os.path.abspath(dirpath)
        poster_path = os.path.join(current_abs_dirpath, "poster.jpg")
        needs_generation = False

        # 1. 检查是否已记录处理且当前海报有效
        if current_abs_dirpath in processed_dirs_set:
            try:
                if os.path.exists(poster_path) and os.path.getsize(poster_path) > 0:
                    print(f"⏭️ 已跳过 (已记录且海报有效): {current_abs_dirpath}")
                    continue
                else:
                    # 已记录，但海报无效或丢失，需要重新生成
                    print(f"⚠️ {current_abs_dirpath} 已记录但海报无效/丢失，尝试重新生成...")
                    needs_generation = True
            except OSError as e: # getsize 可能因文件刚被删除等原因出错
                print(f"⚠️ 检查已记录目录 {current_abs_dirpath} 的海报时出错: {e}。尝试重新生成...")
                needs_generation = True
        
        # 2. 如果未跳过，则检查当前海报状态 (处理未记录的情况或需要生成的情况)
        if not needs_generation: # 仅在未因“已记录但海报无效”而设置 needs_generation 时执行
            if os.path.exists(poster_path):
                try:
                    poster_size = os.path.getsize(poster_path)
                    if poster_size == 0:
                        print(f"🟡 poster.jpg 存在但为空 (0KB): {poster_path}。尝试重新生成...")
                        needs_generation = True
                    else:
                        # 海报存在且不为空，但未在日志中 (例如手动添加或上次运行中断前未记录)
                        print(f"✅ poster.jpg 已存在且不为空: {poster_path} (将记录到日志)。")
                        mark_dir_as_processed(current_abs_dirpath, processed_dirs_set, PROCESSED_DIRS_LOG_FILE)
                        continue # 已处理，跳过本轮生成
                except OSError as e:
                    print(f"⚠️ 检查 {poster_path} 大小时出错: {e}。尝试重新生成...")
                    needs_generation = True
            else:
                # 海报不存在
                print(f"ℹ️ poster.jpg 不存在于: {poster_path}。尝试生成...")
                needs_generation = True

        # 3. 如果确定需要生成海报
        if needs_generation:
            valid_video_files = []
            video_files_in_dir = [f for f in filenames if f.lower().endswith(VIDEO_EXTENSIONS)]
            for vf in video_files_in_dir:
                full_vf_path = os.path.join(current_abs_dirpath, vf)
                if os.path.isfile(full_vf_path) or \
                   (os.path.islink(full_vf_path) and os.path.isfile(os.path.realpath(full_vf_path))):
                    valid_video_files.append(vf)

            if not valid_video_files:
                print(f"⚠️ 在 {current_abs_dirpath} 中没有找到有效的视频文件来生成 poster.jpg。")
                continue

            selected_video_filename = random.choice(valid_video_files)
            print(f"ℹ️ 为 {current_abs_dirpath} 选择视频 '{selected_video_filename}' 生成海报。")
            
            if generate_poster(current_abs_dirpath, selected_video_filename):
                # 成功生成后，再次确认文件有效并标记
                try:
                    if os.path.exists(poster_path) and os.path.getsize(poster_path) > 0:
                        mark_dir_as_processed(current_abs_dirpath, processed_dirs_set, PROCESSED_DIRS_LOG_FILE)
                    else:
                        # 这种情况理论上 generate_poster 应该返回 False
                        print(f"⁉️ generate_poster 声称成功，但 {poster_path} 仍然无效或丢失。")
                except OSError as e:
                    print(f"⁉️ 标记处理时检查 {poster_path} 出错: {e}")
            # else: generate_poster 内部已打印失败信息

    print("✨ 所有处理完成。")

if __name__ == "__main__":
    main()