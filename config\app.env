GITHUB_TOKEN=''
API_TOKEN='moviepilot'
TMDB_API_DOMAIN='api.themoviedb.org'
TMDB_IMAGE_DOMAIN='image.tmdb.org'
WALLPAPER='tmdb'
RECOGNIZE_SOURCE='themoviedb'
SCRAP_FOLLOW_TMDB='True'
AUTO_DOWNLOAD_USER=''
OCR_HOST='https://movie-pilot.org'
DOWNLOAD_SUBTITLE='True'
PLUGIN_MARKET='https://github.com/jxxghp/MoviePilot-Plugins,https://github.com/thsrite/MoviePilot-Plugins,https://github.com/honue/MoviePilot-Plugins,https://github.com/InfinityPacer/MoviePilot-Plugins，https://github.com/jxxghp/MoviePilot-Plugins/,https://github.com/thsrite/MoviePilot-Plugins/,https://github.com/honue/MoviePilot-Plugins/,https://github.com/InfinityPacer/MoviePilot-Plugins/,https://github.com/dandkong/MoviePilot-Plugins/,https://github.com/Aqr-K/MoviePilot-Plugins/,https://github.com/AnjoyLi/MoviePilot-Plugins/,https://github.com/WithdewHua/MoviePilot-Plugins/,https://github.com/HankunYu/MoviePilot-Plugins/,https://github.com/baozaodetudou/MoviePilot-Plugins/,https://github.com/almus2zhang/MoviePilot-Plugins/,https://github.com/Pixel-LH/MoviePilot-Plugins/,https://github.com/lightolly/MoviePilot-Plugins/,https://github.com/suraxiuxiu/MoviePilot-Plugins/,https://github.com/gxterry/MoviePilot-Plugins/,https://github.com/hotlcc/MoviePilot-Plugins-Third/,https://github.com/boeto/MoviePilot-Plugins/,https://github.com/xiangt920/MoviePilot-Plugins/,https://github.com/yubanmeiqin9048/MoviePilot-Plugins/,https://github.com/loongcheung/MoviePilot-Plugins/,https://github.com/xcehnz/MoviePilot-Plugins/,https://github.com/imaliang/MoviePilot-Plugins/'
MOVIE_RENAME_FORMAT='{{title}}{% if year %} ({{year}}){% endif %}/{{title}}{% if year %} ({{year}}){% endif %}{% if part %}-{{part}}{% endif %}{% if videoFormat %} - {{videoFormat}}{% endif %}{% if edition %}.{{edition}}{% endif %}{% if videoCodec %}.{{videoCodec}}{% endif %}{% if audioCodec %}.{{audioCodec}}{% endif %}{% if releaseGroup %}-{{releaseGroup}}{% endif %}{{fileExt}}'
TV_RENAME_FORMAT='{% if season %}{% set season = \'{:02d}\'.format(season | int) %}{% endif %}{{title}}{% if year %} ({{year}}){% endif %}/Season {{season}}/{{title}}{% if year %} ({{year}}){% endif %}.{{season_episode}}{% if part %}-{{part}}{% endif %}{% if episode_title %}.{{episode_title}}{% endif %}{% if videoFormat %}-{{videoFormat}}{% endif %}{% if edition %}.{{edition}}-{% endif %}{% if videoCodec %}{{videoCodec}}-{% endif %}{% if audioCodec %}{{audioCodec}}-{% endif %}{% if customization %}{{customization}}@{% endif %}{{fileExt}}'
FANART_ENABLE='True'
DOH_ENABLE='True'
SEARCH_MULTIPLE_NAME='False'
META_CACHE_EXPIRE='0'
GITHUB_PROXY=''
DOH_DOMAINS='api.themoviedb.org,api.tmdb.org,webservice.fanart.tv,api.github.com,github.com,raw.githubusercontent.com,api.telegram.org'
DOH_RESOLVERS='*******,*******,*******,***************'
TRANSFER_TYPE='move'
OVERWRITE_MODE='latest'
TRANSFER_SAME_DISK='True'
MEDIASERVER='emby'
MEDIASERVER_SYNC_INTERVAL='6'
MEDIASERVER_SYNC_BLACKLIST=''
EMBY_HOST='http://*************:8096'
EMBY_PLAY_HOST=''
EMBY_API_KEY='8ddcfda8677a4281b24bef6ede83a60c'
JELLYFIN_HOST=''
JELLYFIN_PLAY_HOST=''
JELLYFIN_API_KEY=''
PLEX_HOST=''
PLEX_PLAY_HOST=''
PLEX_TOKEN=''
COOKIECLOUD_HOST='http://*************:3000/cookiecloud'
COOKIECLOUD_KEY='x2PJEFyVa5Lebz6NTRC1Mc'
COOKIECLOUD_PASSWORD='sHYfJRTXdo4ytz24oQDxsV'
COOKIECLOUD_INTERVAL='1440'
USER_AGENT='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.57'
COOKIECLOUD_ENABLE_LOCAL='True'
COOKIECLOUD_BLACKLIST=''
MESSAGER='telegram'
WECHAT_CORPID=''
WECHAT_APP_SECRET=''
WECHAT_APP_ID=''
WECHAT_PROXY='https://qyapi.weixin.qq.com'
WECHAT_TOKEN=''
WECHAT_ENCODING_AESKEY=''
WECHAT_ADMINS=''
TELEGRAM_TOKEN='**********************************************'
TELEGRAM_CHAT_ID='1280554894'
TELEGRAM_USERS=''
TELEGRAM_ADMINS=''
SLACK_OAUTH_TOKEN=''
SLACK_APP_TOKEN=''
SLACK_CHANNEL=''
SYNOLOGYCHAT_WEBHOOK=''
SYNOLOGYCHAT_TOKEN=''
VOCECHAT_HOST=''
VOCECHAT_API_KEY=''
VOCECHAT_CHANNEL_ID=''
